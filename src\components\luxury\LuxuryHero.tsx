import React, { useRef, useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { ArrowR<PERSON>, Crown, Star, Gem, Sparkles } from 'lucide-react';
import LuxuryButton from './LuxuryButton';
import VIPIndicator from './VIPIndicator';
import { useLuxuryTheme } from '@/contexts/LuxuryThemeContext';
import { useLuxuryParticles, useLuxuryIntersectionObserver } from '@/utils/luxuryAnimations';

interface LuxuryHeroProps {
  title?: string;
  subtitle?: string;
  ctaText?: string;
  backgroundAnimation?: boolean;
}

const LuxuryHero: React.FC<LuxuryHeroProps> = ({
  title = "Elite Promise Academy",
  subtitle = "Where luxury meets learning in an exclusive educational experience designed for extraordinary children",
  ctaText = "Enter VIP Portal",
  backgroundAnimation = true
}) => {
  const heroRef = useRef<HTMLDivElement>(null);
  const particleContainerRef = useRef<HTMLDivElement>(null);
  const imageRef = useRef<HTMLDivElement>(null);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const { theme, isVIP, isElite } = useLuxuryTheme();

  // Add floating particles
  useLuxuryParticles(particleContainerRef, backgroundAnimation && theme.effects.particles);
  useLuxuryIntersectionObserver(heroRef, 'animate-luxury-fade-in');

  // Mouse movement parallax effect
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (heroRef.current) {
        const rect = heroRef.current.getBoundingClientRect();
        const x = (e.clientX - rect.left) / rect.width;
        const y = (e.clientY - rect.top) / rect.height;
        setMousePosition({ x, y });
      }
    };

    if (backgroundAnimation) {
      window.addEventListener('mousemove', handleMouseMove);
      return () => window.removeEventListener('mousemove', handleMouseMove);
    }
  }, [backgroundAnimation]);

  const parallaxStyle = backgroundAnimation ? {
    transform: `translate(${mousePosition.x * 20}px, ${mousePosition.y * 20}px)`
  } : {};

  return (
    <div 
      ref={heroRef}
      className="relative min-h-screen flex items-center justify-center overflow-hidden"
      style={{
        background: 'linear-gradient(135deg, #1B1B3A 0%, #0F0F23 50%, #2D1B69 100%)'
      }}
    >
      {/* Animated Background Particles */}
      <div 
        ref={particleContainerRef}
        className="absolute inset-0 pointer-events-none"
        style={parallaxStyle}
      />

      {/* Floating 3D Elements */}
      {backgroundAnimation && theme.effects.animations && (
        <>
          <Crown className="absolute top-20 left-20 h-8 w-8 text-luxury-gold/30 animate-luxury-float" style={{ animationDelay: '0s' }} />
          <Star className="absolute top-40 right-32 h-6 w-6 text-luxury-platinum/40 animate-luxury-float" style={{ animationDelay: '1s' }} />
          <Gem className="absolute bottom-32 left-40 h-7 w-7 text-luxury-emerald/35 animate-luxury-float" style={{ animationDelay: '2s' }} />
          <Sparkles className="absolute top-60 left-1/4 h-5 w-5 text-luxury-rose-gold/40 animate-luxury-float" style={{ animationDelay: '0.5s' }} />
          <Crown className="absolute bottom-20 right-20 h-6 w-6 text-luxury-gold/25 animate-luxury-float" style={{ animationDelay: '1.5s' }} />
        </>
      )}

      {/* Main Content */}
      <div className="luxury-container relative z-10">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Text Content */}
          <div className="flex flex-col space-y-8 text-center lg:text-left">
            {/* VIP Status Indicator */}
            {isVIP && (
              <div className="flex justify-center lg:justify-start animate-luxury-scale">
                <VIPIndicator level={theme.premiumLevel} size="lg" animated={true} />
              </div>
            )}

            {/* Main Title */}
            <div className="space-y-4">
              <h1 className="text-5xl md:text-6xl lg:text-7xl font-playfair font-bold leading-tight animate-luxury-fade-in">
                <span className="luxury-heading block">
                  {title}
                </span>
              </h1>
              
              {/* Subtitle */}
              <p className="text-xl md:text-2xl text-white/90 leading-relaxed font-montserrat animate-luxury-fade-in" style={{ animationDelay: '0.3s' }}>
                {subtitle}
              </p>
            </div>
            
            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start animate-luxury-fade-in" style={{ animationDelay: '0.6s' }}>
              <LuxuryButton
                variant="gold"
                size="lg"
                glow={true}
                sparkles={true}
                soundEffect={theme.effects.sounds}
                className="group"
              >
                <Link to="/login" className="flex items-center gap-3">
                  <Crown className="h-5 w-5 group-hover:animate-luxury-sparkle" />
                  {ctaText}
                  <ArrowRight className="h-5 w-5 group-hover:translate-x-1 transition-transform" />
                </Link>
              </LuxuryButton>
              
              <LuxuryButton
                variant="platinum"
                size="lg"
                glow={false}
                sparkles={false}
                soundEffect={theme.effects.sounds}
                className="group"
              >
                <Link to="/login" className="flex items-center gap-3">
                  <Star className="h-5 w-5" />
                  Staff Access
                </Link>
              </LuxuryButton>
            </div>
            
            {/* Trust Indicators */}
            <div className="flex flex-wrap gap-6 items-center justify-center lg:justify-start mt-8 animate-luxury-fade-in" style={{ animationDelay: '0.9s' }}>
              <div className="flex -space-x-4">
                <div className="w-12 h-12 rounded-full bg-luxury-gold flex items-center justify-center text-luxury-navy font-bold text-sm shadow-luxury-gold">
                  100%
                </div>
                <div className="w-12 h-12 rounded-full bg-luxury-emerald flex items-center justify-center text-white font-bold text-sm shadow-luxury-emerald">
                  A+
                </div>
                <div className="w-12 h-12 rounded-full bg-luxury-purple flex items-center justify-center text-white font-bold text-sm shadow-luxury-purple">
                  5★
                </div>
              </div>
              <span className="text-white/80 font-montserrat">Trusted by elite families worldwide</span>
            </div>
          </div>
          
          {/* Hero Image */}
          <div className="relative animate-luxury-scale" style={{ animationDelay: '0.4s' }}>
            {/* Background Glow Effects */}
            <div className="absolute -top-20 -right-20 w-80 h-80 bg-luxury-gold/20 rounded-full blur-3xl animate-luxury-glow" />
            <div className="absolute -bottom-20 -left-20 w-96 h-96 bg-luxury-purple/20 rounded-full blur-3xl animate-luxury-glow" style={{ animationDelay: '1s' }} />
            
            <div 
              ref={imageRef}
              className="relative z-10 luxury-card p-8 rounded-3xl luxury-hover-lift"
              style={parallaxStyle}
            >
              <img 
                src="https://images.unsplash.com/photo-1518495973542-4542c06a5843" 
                alt="Elite children in luxury learning environment" 
                className="w-full h-auto rounded-2xl shadow-luxury"
              />
              
              {/* Floating Achievement Badges */}
              <div className="absolute -right-6 -top-6 luxury-card p-4 rounded-2xl animate-luxury-float">
                <div className="bg-luxury-emerald/20 p-3 rounded-xl">
                  <span className="font-montserrat font-bold text-luxury-emerald text-sm">
                    🏆 Elite Excellence
                  </span>
                </div>
              </div>
              
              <div className="absolute -left-6 -bottom-6 luxury-card p-4 rounded-2xl animate-luxury-float" style={{ animationDelay: '1s' }}>
                <div className="bg-luxury-gold/20 p-3 rounded-xl">
                  <span className="font-montserrat font-bold text-luxury-gold text-sm">
                    👑 VIP Learning
                  </span>
                </div>
              </div>

              {/* Sparkle Effects */}
              {theme.effects.animations && (
                <>
                  <Sparkles className="absolute top-4 right-4 h-4 w-4 text-luxury-gold animate-luxury-sparkle" />
                  <Sparkles className="absolute bottom-4 left-4 h-4 w-4 text-luxury-platinum animate-luxury-sparkle" style={{ animationDelay: '0.7s' }} />
                </>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Gradient Overlay */}
      <div className="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-black/20 to-transparent" />
    </div>
  );
};

export default LuxuryHero;
