import { useEffect, useRef } from 'react';

// Luxury animation utilities
export const createSparkleEffect = (element: HTMLElement) => {
  const sparkle = document.createElement('div');
  sparkle.className = 'absolute w-2 h-2 bg-luxury-gold rounded-full animate-luxury-sparkle pointer-events-none';
  sparkle.style.left = Math.random() * 100 + '%';
  sparkle.style.top = Math.random() * 100 + '%';
  
  element.appendChild(sparkle);
  
  setTimeout(() => {
    sparkle.remove();
  }, 2000);
};

export const createFloatingParticle = (container: HTMLElement, type: 'star' | 'crown' | 'gem' = 'star') => {
  const particle = document.createElement('div');
  particle.className = `absolute pointer-events-none animate-luxury-float`;
  
  const icons = {
    star: '⭐',
    crown: '👑',
    gem: '💎'
  };
  
  particle.innerHTML = icons[type];
  particle.style.fontSize = Math.random() * 20 + 10 + 'px';
  particle.style.left = Math.random() * 100 + '%';
  particle.style.top = Math.random() * 100 + '%';
  particle.style.animationDelay = Math.random() * 2 + 's';
  particle.style.animationDuration = (Math.random() * 4 + 4) + 's';
  
  container.appendChild(particle);
  
  setTimeout(() => {
    particle.remove();
  }, 8000);
};

export const useLuxuryParticles = (containerRef: React.RefObject<HTMLElement>, enabled: boolean = true) => {
  useEffect(() => {
    if (!enabled || !containerRef.current) return;
    
    const container = containerRef.current;
    const particleTypes: ('star' | 'crown' | 'gem')[] = ['star', 'crown', 'gem'];
    
    const createParticle = () => {
      const type = particleTypes[Math.floor(Math.random() * particleTypes.length)];
      createFloatingParticle(container, type);
    };
    
    // Create initial particles
    for (let i = 0; i < 5; i++) {
      setTimeout(createParticle, i * 1000);
    }
    
    // Continue creating particles
    const interval = setInterval(createParticle, 3000);
    
    return () => clearInterval(interval);
  }, [containerRef, enabled]);
};

export const useLuxurySparkles = (elementRef: React.RefObject<HTMLElement>, enabled: boolean = true) => {
  useEffect(() => {
    if (!enabled || !elementRef.current) return;
    
    const element = elementRef.current;
    
    const createSparkle = () => {
      createSparkleEffect(element);
    };
    
    const interval = setInterval(createSparkle, 1500);
    
    return () => clearInterval(interval);
  }, [elementRef, enabled]);
};

export const useLuxuryHover = (elementRef: React.RefObject<HTMLElement>) => {
  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;
    
    const handleMouseEnter = () => {
      element.classList.add('animate-luxury-glow');
      createSparkleEffect(element);
    };
    
    const handleMouseLeave = () => {
      element.classList.remove('animate-luxury-glow');
    };
    
    element.addEventListener('mouseenter', handleMouseEnter);
    element.addEventListener('mouseleave', handleMouseLeave);
    
    return () => {
      element.removeEventListener('mouseenter', handleMouseEnter);
      element.removeEventListener('mouseleave', handleMouseLeave);
    };
  }, [elementRef]);
};

// Intersection Observer for luxury animations
export const useLuxuryIntersectionObserver = (
  elementRef: React.RefObject<HTMLElement>,
  animationClass: string = 'animate-luxury-fade-in'
) => {
  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;
    
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add(animationClass);
          }
        });
      },
      { threshold: 0.1 }
    );
    
    observer.observe(element);
    
    return () => observer.disconnect();
  }, [elementRef, animationClass]);
};

// Luxury gradient utilities
export const getLuxuryGradient = (type: 'gold' | 'platinum' | 'purple' | 'emerald' | 'rose-gold' | 'rainbow') => {
  const gradients = {
    gold: 'linear-gradient(135deg, #FFD700 0%, #FFA500 50%, #FF8C00 100%)',
    platinum: 'linear-gradient(135deg, #E5E4E2 0%, #C0C0C0 50%, #A8A8A8 100%)',
    purple: 'linear-gradient(135deg, #663399 0%, #4B0082 50%, #8A2BE2 100%)',
    emerald: 'linear-gradient(135deg, #50C878 0%, #228B22 50%, #32CD32 100%)',
    'rose-gold': 'linear-gradient(135deg, #E8B4B8 0%, #B76E79 50%, #CD919E 100%)',
    rainbow: 'linear-gradient(135deg, #FFD700 0%, #FF69B4 25%, #00BFFF 50%, #50C878 75%, #E8B4B8 100%)'
  };
  
  return gradients[type];
};

// Luxury text gradient utility
export const getLuxuryTextGradient = (type: 'gold-purple' | 'platinum-blue' | 'emerald-gold' | 'rose-purple') => {
  const gradients = {
    'gold-purple': 'linear-gradient(135deg, #FFD700 0%, #663399 100%)',
    'platinum-blue': 'linear-gradient(135deg, #E5E4E2 0%, #00BFFF 100%)',
    'emerald-gold': 'linear-gradient(135deg, #50C878 0%, #FFD700 100%)',
    'rose-purple': 'linear-gradient(135deg, #E8B4B8 0%, #663399 100%)'
  };
  
  return gradients[type];
};

// Luxury timing functions
export const luxuryEasing = {
  gentle: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
  elegant: 'cubic-bezier(0.23, 1, 0.32, 1)',
  premium: 'cubic-bezier(0.165, 0.84, 0.44, 1)',
  royal: 'cubic-bezier(0.19, 1, 0.22, 1)'
};

// Sound effects (optional)
export const playLuxurySound = (type: 'click' | 'hover' | 'success' | 'sparkle') => {
  // This would integrate with a sound library
  // For now, we'll use a simple implementation
  if ('AudioContext' in window) {
    const audioContext = new AudioContext();
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();
    
    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);
    
    const frequencies = {
      click: 800,
      hover: 600,
      success: 1000,
      sparkle: 1200
    };
    
    oscillator.frequency.setValueAtTime(frequencies[type], audioContext.currentTime);
    oscillator.type = 'sine';
    
    gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);
    
    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + 0.1);
  }
};
