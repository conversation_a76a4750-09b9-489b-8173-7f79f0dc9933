import React, { useRef } from 'react';
import { Crown, Star, Gem, Shield, Award, Sparkles, Heart, BookOpen } from 'lucide-react';
import PremiumBadge from './PremiumBadge';
import { useLuxuryTheme } from '@/contexts/LuxuryThemeContext';
import { useLuxuryIntersectionObserver, useLuxuryHover } from '@/utils/luxuryAnimations';

interface LuxuryFeature {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  premium: boolean;
  color: 'gold' | 'platinum' | 'purple' | 'emerald' | 'rose-gold';
  badge?: 'exclusive' | 'limited' | 'vip-only' | 'elite-access' | 'premium' | 'featured';
}

interface ExclusiveFeaturesProps {
  layout?: 'grid' | 'carousel';
  showBadges?: boolean;
}

const ExclusiveFeatures: React.FC<ExclusiveFeaturesProps> = ({
  layout = 'grid',
  showBadges = true
}) => {
  const sectionRef = useRef<HTMLDivElement>(null);
  const { theme, isVIP, isElite } = useLuxuryTheme();

  useLuxuryIntersectionObserver(sectionRef, 'animate-luxury-fade-in');

  const features: LuxuryFeature[] = [
    {
      id: '1',
      title: 'Elite Curriculum',
      description: 'Bespoke educational programs designed exclusively for gifted children, featuring advanced cognitive development and creative excellence.',
      icon: <Crown className="h-8 w-8" />,
      premium: true,
      color: 'gold',
      badge: 'exclusive'
    },
    {
      id: '2',
      title: 'VIP Educators',
      description: 'World-class teachers with advanced degrees and specialized training in luxury early childhood education.',
      icon: <Star className="h-8 w-8" />,
      premium: true,
      color: 'platinum',
      badge: 'vip-only'
    },
    {
      id: '3',
      title: 'Premium Environment',
      description: 'State-of-the-art facilities with luxury amenities, ensuring the highest standards of safety and comfort.',
      icon: <Gem className="h-8 w-8" />,
      premium: true,
      color: 'emerald',
      badge: 'premium'
    },
    {
      id: '4',
      title: 'Elite Spaces',
      description: 'Architecturally designed learning environments that inspire creativity and foster intellectual growth.',
      icon: <Shield className="h-8 w-8" />,
      premium: false,
      color: 'purple',
      badge: 'featured'
    },
    {
      id: '5',
      title: 'Luxury Care',
      description: 'Personalized attention with low student-to-teacher ratios, ensuring each child receives premium care.',
      icon: <Heart className="h-8 w-8" />,
      premium: true,
      color: 'rose-gold',
      badge: 'elite-access'
    },
    {
      id: '6',
      title: 'Advanced Learning',
      description: 'Cutting-edge educational technology and innovative teaching methods for accelerated development.',
      icon: <BookOpen className="h-8 w-8" />,
      premium: false,
      color: 'gold',
      badge: 'limited'
    }
  ];

  const getCardClass = (feature: LuxuryFeature) => {
    const baseClass = 'luxury-card p-8 rounded-3xl luxury-hover-lift transition-all duration-500 group relative overflow-hidden';
    
    const colorClasses = {
      gold: 'border-luxury-gold/30 hover:border-luxury-gold/60',
      platinum: 'border-luxury-platinum/30 hover:border-luxury-platinum/60',
      purple: 'border-luxury-purple/30 hover:border-luxury-purple/60',
      emerald: 'border-luxury-emerald/30 hover:border-luxury-emerald/60',
      'rose-gold': 'border-luxury-rose-gold/30 hover:border-luxury-rose-gold/60'
    };
    
    return `${baseClass} ${colorClasses[feature.color]}`;
  };

  const getIconClass = (feature: LuxuryFeature) => {
    const colorClasses = {
      gold: 'text-luxury-gold bg-luxury-gold/20',
      platinum: 'text-luxury-platinum bg-luxury-platinum/20',
      purple: 'text-luxury-purple bg-luxury-purple/20',
      emerald: 'text-luxury-emerald bg-luxury-emerald/20',
      'rose-gold': 'text-luxury-rose-gold bg-luxury-rose-gold/20'
    };
    
    return `inline-flex items-center justify-center p-4 rounded-2xl mb-6 group-hover:scale-110 transition-transform duration-300 ${colorClasses[feature.color]}`;
  };

  const FeatureCard: React.FC<{ feature: LuxuryFeature; index: number }> = ({ feature, index }) => {
    const cardRef = useRef<HTMLDivElement>(null);
    useLuxuryHover(cardRef);

    return (
      <div 
        ref={cardRef}
        className={getCardClass(feature)}
        style={{ animationDelay: `${index * 0.1}s` }}
      >
        {/* Background Glow Effect */}
        <div className={`absolute inset-0 bg-gradient-to-br from-${feature.color}/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500`} />
        
        {/* Premium Badge */}
        {showBadges && feature.badge && (
          <div className="absolute top-4 right-4">
            <PremiumBadge type={feature.badge} size="sm" animated={theme.effects.animations} />
          </div>
        )}

        {/* Sparkle Effects */}
        {theme.effects.animations && feature.premium && (
          <>
            <Sparkles className="absolute top-6 left-6 h-3 w-3 text-luxury-gold/50 animate-luxury-sparkle" />
            <Sparkles className="absolute bottom-6 right-6 h-3 w-3 text-luxury-gold/50 animate-luxury-sparkle" style={{ animationDelay: '1s' }} />
          </>
        )}

        {/* Icon */}
        <div className={getIconClass(feature)}>
          {feature.icon}
        </div>

        {/* Content */}
        <div className="relative z-10">
          <h3 className="text-2xl font-playfair font-bold text-white mb-4 group-hover:text-luxury-gold transition-colors duration-300">
            {feature.title}
          </h3>
          <p className="text-white/80 font-montserrat leading-relaxed group-hover:text-white transition-colors duration-300">
            {feature.description}
          </p>
        </div>

        {/* VIP Access Indicator */}
        {feature.premium && !isVIP && (
          <div className="absolute bottom-4 left-4 text-xs font-bold text-luxury-gold/60">
            VIP ACCESS REQUIRED
          </div>
        )}

        {/* Shimmer Effect */}
        <div className="absolute inset-0 bg-shimmer opacity-0 group-hover:opacity-20 transition-opacity duration-500" />
      </div>
    );
  };

  return (
    <section 
      ref={sectionRef}
      className="luxury-section relative"
      style={{
        background: 'linear-gradient(135deg, #0F0F23 0%, #1B1B3A 50%, #2D1B69 100%)'
      }}
    >
      <div className="luxury-container">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-playfair font-bold luxury-heading mb-6">
            Exclusive Elite Features
          </h2>
          <p className="text-xl text-white/90 font-montserrat max-w-4xl mx-auto leading-relaxed">
            Discover the premium advantages that set Promise Academy apart as the ultimate destination for elite early childhood education.
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <FeatureCard key={feature.id} feature={feature} index={index} />
          ))}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-16">
          <div className="inline-flex items-center gap-2 px-6 py-3 bg-luxury-gold/20 rounded-full text-luxury-gold font-montserrat font-semibold">
            <Crown className="h-5 w-5" />
            <span>Experience Elite Education Today</span>
            <Sparkles className="h-4 w-4 animate-luxury-sparkle" />
          </div>
        </div>
      </div>

      {/* Background Decorative Elements */}
      {theme.effects.animations && (
        <>
          <Crown className="absolute top-20 right-20 h-12 w-12 text-luxury-gold/10 animate-luxury-float" />
          <Star className="absolute bottom-32 left-20 h-10 w-10 text-luxury-platinum/10 animate-luxury-float" style={{ animationDelay: '2s' }} />
          <Gem className="absolute top-1/2 right-10 h-8 w-8 text-luxury-emerald/10 animate-luxury-float" style={{ animationDelay: '1s' }} />
        </>
      )}
    </section>
  );
};

export default ExclusiveFeatures;
