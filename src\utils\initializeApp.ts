


// This function initializes the application
// It runs once when the application starts
export async function initializeApp() {
  const isInitialized = localStorage.getItem("app_initialized");
  
  // Only run initialization once
  if (!isInitialized) {
    console.log("Initializing application...");
    
    try {
      // Create demo users
      
      
      // Mark as initialized
      localStorage.setItem("app_initialized", "true");
    } catch (error) {
      console.error("Error during app initialization:", error);
    }
  }
}
