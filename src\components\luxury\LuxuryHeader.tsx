import React, { useState, useRef, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Menu, Crown, Sparkles } from 'lucide-react';
import LuxuryButton from './LuxuryButton';
import VIPIndicator from './VIPIndicator';
import { useLuxuryTheme } from '@/contexts/LuxuryThemeContext';
import { useLuxurySparkles, useLuxuryIntersectionObserver } from '@/utils/luxuryAnimations';

interface LuxuryHeaderProps {
  onAboutClick?: () => void;
  onProgramsClick?: () => void;
  onContactClick?: () => void;
  showVIPIndicator?: boolean;
}

const LuxuryHeader: React.FC<LuxuryHeaderProps> = ({
  onAboutClick,
  onProgramsClick,
  onContactClick,
  showVIPIndicator = true
}) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const headerRef = useRef<HTMLElement>(null);
  const logoRef = useRef<HTMLDivElement>(null);
  const { theme, isVIP } = useLuxuryTheme();

  // Add sparkle effects to logo
  useLuxurySparkles(logoRef, theme.effects.animations);
  useLuxuryIntersectionObserver(headerRef, 'animate-luxury-fade-in');

  // Handle scroll effects
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const isHomePage = window.location.pathname === "/";

  const headerClass = `
    fixed top-0 left-0 right-0 z-50 transition-all duration-500 ease-out
    ${isScrolled 
      ? 'bg-white/10 backdrop-blur-md border-b border-luxury-gold/20 shadow-luxury' 
      : 'bg-transparent'
    }
  `;

  return (
    <header ref={headerRef} className={headerClass}>
      <div className="luxury-container">
        <div className="flex justify-between items-center py-4">
          {/* Luxury Logo */}
          <Link to="/" className="flex items-center space-x-3 group">
            <div 
              ref={logoRef}
              className="relative h-12 w-12 bg-luxury-gold rounded-xl flex items-center justify-center shadow-luxury-gold animate-luxury-glow group-hover:scale-110 transition-transform duration-300"
            >
              <Crown className="h-6 w-6 text-luxury-navy animate-luxury-float" />
              
              {/* Sparkle effects around logo */}
              {theme.effects.animations && (
                <>
                  <Sparkles className="absolute -top-1 -right-1 h-3 w-3 text-luxury-gold animate-luxury-sparkle" />
                  <Sparkles className="absolute -bottom-1 -left-1 h-3 w-3 text-luxury-gold animate-luxury-sparkle" style={{ animationDelay: '1s' }} />
                </>
              )}
            </div>
            
            <div className="flex flex-col">
              <span className="text-xl font-playfair font-bold luxury-heading">
                Promise Academy
              </span>
              <span className="text-xs font-montserrat text-luxury-gold/80 tracking-wider">
                ELITE EDUCATION
              </span>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            {isHomePage ? (
              <>
                <button 
                  onClick={onAboutClick} 
                  className="font-montserrat font-medium text-white/90 hover:text-luxury-gold transition-all duration-300 hover:scale-105 luxury-hover-glow"
                >
                  About
                </button>
                <button 
                  onClick={onProgramsClick} 
                  className="font-montserrat font-medium text-white/90 hover:text-luxury-gold transition-all duration-300 hover:scale-105 luxury-hover-glow"
                >
                  Programs
                </button>
                <button 
                  onClick={onContactClick} 
                  className="font-montserrat font-medium text-white/90 hover:text-luxury-gold transition-all duration-300 hover:scale-105 luxury-hover-glow"
                >
                  Contact
                </button>
              </>
            ) : (
              <Link 
                to="/" 
                className="font-montserrat font-medium text-white/90 hover:text-luxury-gold transition-all duration-300 hover:scale-105 luxury-hover-glow"
              >
                Home
              </Link>
            )}
            
            {/* VIP Indicator */}
            {showVIPIndicator && isVIP && (
              <VIPIndicator size="sm" animated={true} />
            )}
            
            {/* Premium Login Button */}
            <LuxuryButton
              variant="gold"
              size="sm"
              glow={true}
              sparkles={true}
              soundEffect={theme.effects.sounds}
            >
              <Link to="/login" className="flex items-center gap-2">
                <Crown className="h-4 w-4" />
                VIP Portal
              </Link>
            </LuxuryButton>
          </nav>

          {/* Mobile Menu Button */}
          <button 
            className="md:hidden rounded-xl p-3 text-white/90 hover:bg-luxury-gold/20 hover:text-luxury-gold transition-all duration-300 luxury-hover-glow" 
            onClick={toggleMenu}
          >
            <Menu className="h-6 w-6" />
          </button>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden py-6 border-t border-luxury-gold/20 bg-white/5 backdrop-blur-md rounded-b-2xl animate-luxury-fade-in">
            <nav className="flex flex-col space-y-4">
              {isHomePage ? (
                <>
                  <button 
                    onClick={() => {
                      onAboutClick?.();
                      setIsMenuOpen(false);
                    }} 
                    className="font-montserrat font-medium text-white/90 hover:text-luxury-gold transition-all duration-300 px-4 py-2 text-left luxury-hover-glow"
                  >
                    About
                  </button>
                  <button 
                    onClick={() => {
                      onProgramsClick?.();
                      setIsMenuOpen(false);
                    }} 
                    className="font-montserrat font-medium text-white/90 hover:text-luxury-gold transition-all duration-300 px-4 py-2 text-left luxury-hover-glow"
                  >
                    Programs
                  </button>
                  <button 
                    onClick={() => {
                      onContactClick?.();
                      setIsMenuOpen(false);
                    }} 
                    className="font-montserrat font-medium text-white/90 hover:text-luxury-gold transition-all duration-300 px-4 py-2 text-left luxury-hover-glow"
                  >
                    Contact
                  </button>
                </>
              ) : (
                <Link 
                  to="/" 
                  className="font-montserrat font-medium text-white/90 hover:text-luxury-gold transition-all duration-300 px-4 py-2 luxury-hover-glow"
                  onClick={() => setIsMenuOpen(false)}
                >
                  Home
                </Link>
              )}
              
              {/* Mobile VIP Indicator */}
              {showVIPIndicator && isVIP && (
                <div className="px-4 py-2">
                  <VIPIndicator size="sm" animated={true} />
                </div>
              )}
              
              {/* Mobile Login Button */}
              <div className="px-4 py-2">
                <LuxuryButton
                  variant="gold"
                  size="sm"
                  glow={true}
                  sparkles={true}
                  soundEffect={theme.effects.sounds}
                  className="w-full justify-center"
                  onClick={() => setIsMenuOpen(false)}
                >
                  <Link to="/login" className="flex items-center gap-2">
                    <Crown className="h-4 w-4" />
                    VIP Portal
                  </Link>
                </LuxuryButton>
              </div>
            </nav>
          </div>
        )}
      </div>
    </header>
  );
};

export default LuxuryHeader;
