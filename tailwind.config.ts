
import type { Config } from "tailwindcss";

export default {
	darkMode: ["class"],
	content: [
		"./pages/**/*.{ts,tsx}",
		"./components/**/*.{ts,tsx}",
		"./app/**/*.{ts,tsx}",
		"./src/**/*.{ts,tsx}",
	],
	prefix: "",
	theme: {
		container: {
			center: true,
			padding: '2rem',
			screens: {
				'2xl': '1400px'
			}
		},
		extend: {
			colors: {
				border: 'hsl(var(--border))',
				input: 'hsl(var(--input))',
				ring: 'hsl(var(--ring))',
				background: 'hsl(var(--background))',
				foreground: 'hsl(var(--foreground))',
				primary: {
					DEFAULT: 'hsl(var(--primary))',
					foreground: 'hsl(var(--primary-foreground))'
				},
				secondary: {
					DEFAULT: 'hsl(var(--secondary))',
					foreground: 'hsl(var(--secondary-foreground))'
				},
				destructive: {
					DEFAULT: 'hsl(var(--destructive))',
					foreground: 'hsl(var(--destructive-foreground))'
				},
				muted: {
					DEFAULT: 'hsl(var(--muted))',
					foreground: 'hsl(var(--muted-foreground))'
				},
				accent: {
					DEFAULT: 'hsl(var(--accent))',
					foreground: 'hsl(var(--accent-foreground))'
				},
				popover: {
					DEFAULT: 'hsl(var(--popover))',
					foreground: 'hsl(var(--popover-foreground))'
				},
				card: {
					DEFAULT: 'hsl(var(--card))',
					foreground: 'hsl(var(--card-foreground))'
				},
				// Luxury Elite Color Palette
				'luxury-gold': {
					'50': '#fffef7',
					'100': '#fffbeb',
					'200': '#fef3c7',
					'300': '#fde68a',
					'400': '#fcd34d',
					'500': '#f59e0b',
					'600': '#d97706',
					'700': '#b45309',
					'800': '#92400e',
					'900': '#78350f',
					'950': '#451a03',
					DEFAULT: '#FFD700',
					dark: '#B8860B'
				},
				'luxury-platinum': {
					'50': '#f8f9fa',
					'100': '#f1f3f4',
					'200': '#e8eaed',
					'300': '#dadce0',
					'400': '#bdc1c6',
					'500': '#9aa0a6',
					'600': '#80868b',
					'700': '#5f6368',
					'800': '#3c4043',
					'900': '#202124',
					DEFAULT: '#E5E4E2',
					dark: '#C0C0C0'
				},
				'luxury-purple': {
					'50': '#faf5ff',
					'100': '#f3e8ff',
					'200': '#e9d5ff',
					'300': '#d8b4fe',
					'400': '#c084fc',
					'500': '#a855f7',
					'600': '#9333ea',
					'700': '#7c3aed',
					'800': '#6b21a8',
					'900': '#581c87',
					'950': '#3b0764',
					DEFAULT: '#663399',
					dark: '#4B0082'
				},
				'luxury-emerald': {
					'50': '#ecfdf5',
					'100': '#d1fae5',
					'200': '#a7f3d0',
					'300': '#6ee7b7',
					'400': '#34d399',
					'500': '#10b981',
					'600': '#059669',
					'700': '#047857',
					'800': '#065f46',
					'900': '#064e3b',
					'950': '#022c22',
					DEFAULT: '#50C878',
					dark: '#228B22'
				},
				'luxury-rose-gold': {
					'50': '#fdf2f8',
					'100': '#fce7f3',
					'200': '#fbcfe8',
					'300': '#f9a8d4',
					'400': '#f472b6',
					'500': '#ec4899',
					'600': '#db2777',
					'700': '#be185d',
					'800': '#9d174d',
					'900': '#831843',
					'950': '#500724',
					DEFAULT: '#E8B4B8',
					dark: '#B76E79'
				},
				'luxury-navy': {
					'50': '#f0f4ff',
					'100': '#e0e7ff',
					'200': '#c7d2fe',
					'300': '#a5b4fc',
					'400': '#818cf8',
					'500': '#6366f1',
					'600': '#4f46e5',
					'700': '#4338ca',
					'800': '#3730a3',
					'900': '#312e81',
					'950': '#1e1b4b',
					DEFAULT: '#1B1B3A',
					dark: '#0F0F23'
				},
				// Playful Accent Colors for Children
				'playful-pink': {
					DEFAULT: '#FF69B4',
					light: '#FFB6C1',
					dark: '#C71585'
				},
				'playful-blue': {
					DEFAULT: '#00BFFF',
					light: '#87CEEB',
					dark: '#0080FF'
				},
				'playful-yellow': {
					DEFAULT: '#FFD700',
					light: '#FFFF99',
					dark: '#DAA520'
				},
				'playful-coral': {
					DEFAULT: '#FF7F50',
					light: '#FFA07A',
					dark: '#FF6347'
				},
				'playful-mint': {
					DEFAULT: '#98FB98',
					light: '#AFEEEE',
					dark: '#00FA9A'
				},
				// Promise Academy custom colors
				'promise': {
					'50': '#eefaff',
					'100': '#dcf5ff',
					'200': '#b2eeff',
					'300': '#6ce2ff',
					'400': '#21d0fc',
					'500': '#03b8e5',
					'600': '#0094c1',
					'700': '#00769c',
					'800': '#015f7d',
					'900': '#063c4f',
					'950': '#042a38',
					'foreground': '#ffffff'
				},
				'meadow': {
					'50': '#eafce8',
					'100': '#d1f7d1',
					'200': '#a4eca5',
					'300': '#6ed971',
					'400': '#42bf48',
					'500': '#28a12f',
					'600': '#1b8424',
					'700': '#186922',
					'800': '#17531f',
					'900': '#15451c',
					'950': '#06260c',
					'foreground': '#ffffff'
				},
				'sunlight': {
					'50': '#fdffe7',
					'100': '#f8ffc1',
					'200': '#f1fd86',
					'300': '#e9f842',
					'400': '#daed0f',
					'500': '#bed406',
					'600': '#9ba705',
					'700': '#767f0a',
					'800': '#636810',
					'900': '#545911',
					'950': '#2c2f03',
					'foreground': '#000000'
				},
				'peach': {
					'50': '#fff4e8',
					'100': '#ffe5ca',
					'200': '#ffc999',
					'300': '#fea35b',
					'400': '#fc7f2c',
					'500': '#fb5f0d',
					'600': '#e84a06',
					'700': '#c13508',
					'800': '#992c0e',
					'900': '#7c280f',
					'950': '#431204',
					'foreground': '#ffffff'
				},
				'sidebar': {
					DEFAULT: 'hsl(var(--sidebar-background))',
					foreground: 'hsl(var(--sidebar-foreground))',
					primary: 'hsl(var(--sidebar-primary))',
					'primary-foreground': 'hsl(var(--sidebar-primary-foreground))',
					accent: 'hsl(var(--sidebar-accent))',
					'accent-foreground': 'hsl(var(--sidebar-accent-foreground))',
					border: 'hsl(var(--sidebar-border))',
					ring: 'hsl(var(--sidebar-ring))'
				}
			},
			fontFamily: {
				// Luxury Premium Fonts
				'playfair': ['"Playfair Display"', 'serif'],
				'montserrat': ['Montserrat', 'sans-serif'],
				'inter': ['Inter', 'sans-serif'],
				'fredoka': ['"Fredoka One"', 'cursive'],
				// Existing fonts
				'quicksand': ['Quicksand', 'sans-serif'],
				'nunito': ['Nunito', 'sans-serif'],
				'comic-neue': ['"Comic Neue"', 'cursive']
			},
			borderRadius: {
				lg: 'var(--radius)',
				md: 'calc(var(--radius) - 2px)',
				sm: 'calc(var(--radius) - 4px)'
			},
			keyframes: {
				'accordion-down': {
					from: {
						height: '0'
					},
					to: {
						height: 'var(--radix-accordion-content-height)'
					}
				},
				'accordion-up': {
					from: {
						height: 'var(--radix-accordion-content-height)'
					},
					to: {
						height: '0'
					}
				},
				'float': {
					'0%, 100%': { transform: 'translateY(0)' },
					'50%': { transform: 'translateY(-10px)' }
				},
				'bounce-subtle': {
					'0%, 100%': { transform: 'translateY(0)' },
					'50%': { transform: 'translateY(-5px)' }
				},
				// Luxury Animation Keyframes
				'sparkle': {
					'0%, 100%': { opacity: '0', transform: 'scale(0.8)' },
					'50%': { opacity: '1', transform: 'scale(1.2)' }
				},
				'glow': {
					'0%, 100%': { boxShadow: '0 0 5px rgba(255, 215, 0, 0.3)' },
					'50%': { boxShadow: '0 0 20px rgba(255, 215, 0, 0.8)' }
				},
				'luxury-float': {
					'0%, 100%': { transform: 'translateY(0) rotate(0deg)' },
					'33%': { transform: 'translateY(-8px) rotate(1deg)' },
					'66%': { transform: 'translateY(-4px) rotate(-1deg)' }
				},
				'fade-in-up': {
					'0%': { opacity: '0', transform: 'translateY(30px)' },
					'100%': { opacity: '1', transform: 'translateY(0)' }
				},
				'slide-in-right': {
					'0%': { opacity: '0', transform: 'translateX(30px)' },
					'100%': { opacity: '1', transform: 'translateX(0)' }
				},
				'scale-in': {
					'0%': { opacity: '0', transform: 'scale(0.9)' },
					'100%': { opacity: '1', transform: 'scale(1)' }
				},
				'shimmer': {
					'0%': { backgroundPosition: '-200% 0' },
					'100%': { backgroundPosition: '200% 0' }
				}
			},
			animation: {
				'accordion-down': 'accordion-down 0.2s ease-out',
				'accordion-up': 'accordion-up 0.2s ease-out',
				'float': 'float 6s ease-in-out infinite',
				'bounce-subtle': 'bounce-subtle 2s ease-in-out infinite',
				// Luxury Animations
				'sparkle': 'sparkle 2s ease-in-out infinite',
				'glow': 'glow 3s ease-in-out infinite',
				'luxury-float': 'luxury-float 8s ease-in-out infinite',
				'fade-in-up': 'fade-in-up 0.6s ease-out',
				'slide-in-right': 'slide-in-right 0.6s ease-out',
				'scale-in': 'scale-in 0.4s ease-out',
				'shimmer': 'shimmer 2s linear infinite'
			},
			boxShadow: {
				'soft': '0 4px 15px rgba(0, 0, 0, 0.05)',
				'card': '0 10px 25px -5px rgba(0, 0, 0, 0.05)',
				'button': '0 4px 10px rgba(0, 0, 0, 0.1)',
				// Luxury Shadows
				'luxury': '0 8px 32px rgba(255, 215, 0, 0.15)',
				'luxury-gold': '0 4px 20px rgba(255, 215, 0, 0.3)',
				'luxury-purple': '0 4px 20px rgba(102, 51, 153, 0.3)',
				'luxury-emerald': '0 4px 20px rgba(80, 200, 120, 0.3)',
				'luxury-rose': '0 4px 20px rgba(232, 180, 184, 0.3)',
				'glass': '0 8px 32px rgba(31, 38, 135, 0.37)',
				'glow-gold': '0 0 20px rgba(255, 215, 0, 0.5)',
				'glow-purple': '0 0 20px rgba(102, 51, 153, 0.5)'
			},
			backgroundImage: {
				// Luxury Gradients
				'luxury-gold': 'linear-gradient(135deg, #FFD700 0%, #FFA500 50%, #FF8C00 100%)',
				'luxury-platinum': 'linear-gradient(135deg, #E5E4E2 0%, #C0C0C0 50%, #A8A8A8 100%)',
				'luxury-purple': 'linear-gradient(135deg, #663399 0%, #4B0082 50%, #8A2BE2 100%)',
				'luxury-emerald': 'linear-gradient(135deg, #50C878 0%, #228B22 50%, #32CD32 100%)',
				'luxury-rose-gold': 'linear-gradient(135deg, #E8B4B8 0%, #B76E79 50%, #CD919E 100%)',
				'luxury-rainbow': 'linear-gradient(135deg, #FFD700 0%, #FF69B4 25%, #00BFFF 50%, #50C878 75%, #E8B4B8 100%)',
				'shimmer': 'linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent)'
			}
		}
	},
	plugins: [require("tailwindcss-animate")],
} satisfies Config;
